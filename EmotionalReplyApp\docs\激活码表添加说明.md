# 激活码表添加说明

## 🎯 **问题解决**

**问题**：deploy.sql 缺少了激活码表，但系统中有激活码功能的代码
**解决**：重新添加了完整的激活码表结构和测试数据

## 📊 **激活码表结构**

### **表名：activation_codes**

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 激活码ID |
| code | VARCHAR | 32 | UNIQUE, NOT NULL | 激活码 |
| code_type | VARCHAR | 20 | NOT NULL | 激活码类型 |
| duration_days | INT | - | NOT NULL | 有效天数 |
| batch_id | VARCHAR | 64 | - | 批次ID |
| created_by | BIGINT | - | NOT NULL | 创建者(管理员ID) |
| used_by | BIGINT | - | - | 使用者用户ID |
| used_time | DATETIME | - | - | 使用时间 |
| expire_time | DATETIME | - | NOT NULL | 激活码过期时间 |
| status | TINYINT | - | DEFAULT 0 | 状态：0-未使用，1-已使用，2-已过期 |
| remark | VARCHAR | 255 | - | 备注信息 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### **索引设计**
- **主键索引**：id
- **唯一索引**：code
- **普通索引**：batch_id, created_by, used_by, status, expire_time, code_type
- **外键约束**：
  - created_by → users(id) ON DELETE CASCADE
  - used_by → users(id) ON DELETE SET NULL

## 🎫 **激活码类型**

| 类型代码 | 显示名称 | 有效天数 | 说明 |
|---------|----------|----------|------|
| vip_1m | 1个月VIP | 30天 | 1个月VIP会员 |
| vip_3m | 3个月VIP | 90天 | 3个月VIP会员 |
| vip_6m | 6个月VIP | 180天 | 6个月VIP会员 |
| vip_1y | 1年VIP | 365天 | 1年VIP会员 |

## 📝 **测试数据**

### **测试激活码**
```sql
INSERT INTO activation_codes (code, code_type, duration_days, batch_id, created_by, expire_time, status, remark) VALUES
('VIP1-2024-ABCD-EFGH', 'vip_1m', 30, 'BATCH-2024-001', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '1个月VIP测试激活码'),
('VIP3-2024-IJKL-MNOP', 'vip_3m', 90, 'BATCH-2024-001', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '3个月VIP测试激活码'),
('VIP6-2024-QRST-UVWX', 'vip_6m', 180, 'BATCH-2024-002', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '6个月VIP测试激活码'),
('VIP1Y-2024-YZAB-CDEF', 'vip_1y', 365, 'BATCH-2024-002', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '1年VIP测试激活码'),
('USED-2024-TEST-CODE', 'vip_1m', 30, 'BATCH-2024-TEST', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 1, '已使用的测试激活码');
```

### **系统配置**
```sql
('activation_code_types', '{"vip_1m": "1个月VIP", "vip_3m": "3个月VIP", "vip_6m": "6个月VIP", "vip_1y": "1年VIP"}', '激活码类型配置', 'json', 1)
```

## 🔧 **相关代码文件**

### **实体类**
- `ActivationCode.java` - 激活码实体
- `VipActivationCode.java` - VIP激活码实体（使用同一张表）

### **服务类**
- `ActivationCodeService.java` - 激活码服务接口
- `ActivationCodeServiceImpl.java` - 激活码服务实现
- `VipServiceImpl.java` - VIP服务（包含激活码功能）

### **控制器**
- `ActivationCodeController.java` - 激活码管理接口

### **Mapper**
- `ActivationCodeMapper.java` - 激活码数据访问
- `VipActivationCodeMapper.java` - VIP激活码数据访问

## 🚀 **功能特性**

### **管理员功能**
1. **生成激活码**：批量生成不同类型的激活码
2. **查看激活码列表**：查看所有激活码状态
3. **禁用激活码**：禁用未使用的激活码
4. **激活码统计**：查看使用情况统计

### **用户功能**
1. **使用激活码**：输入激活码激活VIP权限
2. **激活码验证**：自动验证激活码有效性
3. **VIP权限激活**：成功使用后自动开通VIP

### **系统功能**
1. **自动过期检查**：定期检查并标记过期激活码
2. **批次管理**：支持批次生成和管理
3. **使用记录**：完整的使用历史记录

## 📊 **数据库完整性**

### **外键关系**
```
activation_codes.created_by → users.id (创建者关联)
activation_codes.used_by → users.id (使用者关联)
```

### **约束检查**
- 激活码唯一性：确保每个激活码只能存在一次
- 状态一致性：使用后状态自动更新
- 时间有效性：过期时间必须大于创建时间

## ✅ **验证方法**

### **1. 表结构验证**
```sql
DESCRIBE activation_codes;
SHOW INDEX FROM activation_codes;
```

### **2. 数据验证**
```sql
SELECT * FROM activation_codes;
SELECT COUNT(*) FROM activation_codes WHERE status = 0; -- 未使用的激活码数量
```

### **3. 功能验证**
```sql
-- 测试激活码使用
UPDATE activation_codes SET status = 1, used_by = 2, used_time = NOW() WHERE code = 'VIP1-2024-ABCD-EFGH';
```

## 🎉 **部署完成**

现在 `deploy.sql` 包含了完整的激活码功能：
- ✅ 激活码表结构
- ✅ 测试激活码数据
- ✅ 系统配置
- ✅ 外键约束
- ✅ 索引优化

激活码功能现在可以正常使用了！🚀
