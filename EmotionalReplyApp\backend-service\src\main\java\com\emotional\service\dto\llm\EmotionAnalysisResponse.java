package com.emotional.service.dto.llm;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 情感分析响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmotionAnalysisResponse {
    
    /**
     * 主要情感类型
     */
    private String primaryEmotion;
    
    /**
     * 情感置信度 (0.0-1.0)
     */
    private Double confidence;
    
    /**
     * 情感强度 (1-5)
     */
    private Integer intensity;
    
    /**
     * 详细情感分析结果
     */
    private Map<String, Double> emotionScores;
    
    /**
     * 情感关键词
     */
    private List<String> keywords;
    
    /**
     * 分析摘要
     */
    private String summary;
    
    /**
     * 建议的回复风格
     */
    private List<String> suggestedStyles;
    
    /**
     * 处理时间（毫秒）
     */
    private Long processingTime;
    
    /**
     * 使用的模型名称
     */
    private String modelName;
    
    /**
     * 是否成功
     */
    private Boolean success = true;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
}
