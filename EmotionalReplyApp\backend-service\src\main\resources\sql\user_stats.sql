-- 用户统计表
CREATE TABLE IF NOT EXISTS `user_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `daily_usage` int(11) NOT NULL DEFAULT '0' COMMENT '当日使用次数',
  `total_usage` int(11) NOT NULL DEFAULT '0' COMMENT '累计使用次数',
  `daily_quota` int(11) NOT NULL DEFAULT '10' COMMENT '当日配额',
  `is_vip` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否VIP用户：0-普通用户，1-VIP用户',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `stat_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户统计表';

-- 插入一些测试数据
INSERT INTO `user_stats` (`user_id`, `stat_date`, `daily_usage`, `total_usage`, `daily_quota`, `is_vip`) VALUES
(1, CURDATE(), 0, 0, -1, 1),  -- 管理员用户，无限制配额
(2, CURDATE(), 3, 15, 10, 0), -- 普通用户
(3, CURDATE(), 8, 25, 10, 0); -- 普通用户
