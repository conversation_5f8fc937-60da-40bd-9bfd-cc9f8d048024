# 数据库部署说明

## 📁 文件说明

- `deploy.sql` - **完整部署脚本**（包含所有表结构和初始数据）

## 🚀 部署步骤

### 1. 创建数据库
```sql
-- 方式1：脚本会自动创建数据库
-- 直接执行 deploy.sql 即可

-- 方式2：手动创建数据库
CREATE DATABASE emotional_reply_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 2. 执行部署脚本
```bash
# 命令行方式
mysql -u root -p < deploy.sql

# 或者在数据库管理工具中执行 deploy.sql 文件
```

### 3. 验证部署
执行脚本后会自动显示：
- 创建的表列表
- 用户配额设置
- 系统配置信息

## 👥 默认用户账号

| 用户类型 | 用户名 | 密码 | 每日配额 | 说明 |
|---------|--------|------|----------|------|
| 管理员 | admin | 123456 | 无限制 | 系统管理员 |
| VIP用户 | vipuser | 123456 | 50次/天 | VIP测试账号 |
| 普通用户 | testuser | 123456 | 5次/天 | 普通测试账号 |

## 📊 配额设置

- **普通用户**：5次/天
- **VIP用户**：50次/天  
- **管理员**：无限制（-1表示无限制）

## 🗄️ 核心表结构

1. **users** - 用户信息表
2. **reply_history** - 回复历史表
3. **user_settings** - 用户设置表
4. **system_config** - 系统配置表
5. **activation_codes** - 激活码表
6. **user_stats** - 用户统计表

## ⚠️ 注意事项

1. **备份数据**：部署前请备份现有数据库
2. **权限检查**：确保MySQL用户有创建数据库和表的权限
3. **字符集**：使用utf8mb4字符集支持emoji表情
4. **密码安全**：生产环境请修改默认密码

## 🔧 故障排除

### 常见问题
1. **权限不足**：确保MySQL用户有足够权限
2. **字符集问题**：确保数据库支持utf8mb4
3. **外键约束**：确保按顺序执行SQL语句

### 重新部署
如需重新部署，可以先删除数据库：
```sql
DROP DATABASE IF EXISTS emotional_reply_db;
```
然后重新执行 `deploy.sql`。
