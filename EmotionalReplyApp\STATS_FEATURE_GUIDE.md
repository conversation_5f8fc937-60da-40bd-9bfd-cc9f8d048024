# 用户统计功能使用指南

## 🎯 功能概述

用户统计功能可以记录和显示用户的真实使用数据，包括：
- 今日使用次数
- 总使用次数  
- 每日配额（管理员无限制，VIP用户100次，普通用户10次）
- 剩余配额

## 🔧 部署步骤

### 1. 数据库初始化
```sql
-- 执行SQL脚本创建user_stats表
source EmotionalReplyApp/backend-service/src/main/resources/sql/user_stats.sql
```

### 2. 启动后端服务
确保Spring Boot应用正常启动，统计相关的接口会自动注册。

### 3. 测试接口
- `GET /api/user/stats/{userId}` - 获取用户统计
- `POST /api/user/usage/{userId}` - 增加使用次数
- `GET /api/user/quota/{userId}` - 检查用户配额

## 📊 数据类型说明

### VIP状态处理
- **数据库存储**: `tinyint(1)` - 0表示普通用户，1表示VIP用户
- **Java实体**: `Integer` - 与数据库保持一致
- **前端显示**: `<PERSON><PERSON>an` - 转换为true/false便于使用

### 类型转换
```java
// 后端：Integer转Boolean判断
boolean isVipUser = isVip != null && isVip == 1;

// 前端：Integer转Boolean
isVip: result.data.isVip === 1
```

## 🚀 使用方法

### 在功能中记录使用统计
```javascript
import { StatsManager } from '../../utils/stats-manager.js'

// 方法1：简单记录
await StatsManager.recordUsage('emotion_reply')

// 方法2：检查配额后使用
const result = await StatsManager.useFeature('emotion_reply', async () => {
  // 你的功能代码
  return await generateEmotionalReply(text)
})
```

### 获取统计数据
```javascript
// 获取完整统计信息
const stats = await StatsManager.getUserStats()

// 检查配额
const quotaInfo = await StatsManager.checkUserQuota()
console.log('剩余配额:', quotaInfo.remainingQuota)
```

## 📝 测试数据

SQL脚本中包含了测试数据：
- 用户ID 1: 管理员，无限制配额（显示∞）
- 用户ID 2: 普通用户，已使用3次，配额10次
- 用户ID 3: 普通用户，已使用8次，配额10次

## 🎭 管理员特权显示

管理员用户的统计显示：
- **今日使用**: 实际使用次数
- **总计使用**: 累计使用次数
- **每日配额**: ∞（无限制）
- **剩余配额**: ∞（无限制）

这样可以让管理员清楚地看到自己拥有特殊权限。

## 🔍 故障排除

### 常见问题
1. **类型转换错误**: 确保VIP状态的Integer/Boolean转换正确
2. **数据库连接**: 确保user_stats表已创建
3. **用户ID获取**: 确保用户已登录且能获取到userId

### 调试方法
```javascript
// 前端调试
console.log('当前用户ID:', UserManager.getCurrentUserId())
console.log('统计数据:', await StatsManager.getUserStats())

// 后端日志
// 查看控制台输出的统计相关日志
```

## 🎉 预期效果

个人资料页面将显示：
- ✅ 真实的今日使用次数（从数据库获取）
- ✅ 真实的总使用次数（累计统计）
- ✅ 动态的每日配额（根据VIP状态）
- ✅ 准确的剩余配额（实时计算）

不再是模拟数据，而是基于真实使用情况的统计！
