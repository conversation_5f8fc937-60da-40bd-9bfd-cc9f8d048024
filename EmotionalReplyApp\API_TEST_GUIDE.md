# 个人资料和修改密码接口测试指南

## 概述

本文档描述了新开发的个人资料更新和密码修改功能的接口和测试方法。

## 后端接口

### 1. 更新用户资料

**接口地址：** `PUT /user/profile/{userId}`

**请求参数：**
```json
{
  "nickname": "新昵称",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "avatar": "https://example.com/avatar.jpg"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "资料更新成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "新昵称",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "https://example.com/avatar.jpg",
    "password": null
  }
}
```

### 2. 修改密码

**接口地址：** `PUT /user/change-password/{userId}`

**请求参数：**
```json
{
  "oldPassword": "oldPassword123",
  "newPassword": "newPassword123",
  "confirmPassword": "newPassword123"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

## 前端页面

### 1. 个人资料页面

**路径：** `/pages/user/profile`

**功能：**
- 显示用户当前资料信息
- 支持修改昵称、邮箱、手机号
- 支持更换头像
- 保存修改并同步到后端

**测试步骤：**
1. 登录应用
2. 进入设置页面
3. 点击"个人资料"
4. 修改任意字段
5. 点击"保存修改"
6. 验证修改是否成功

### 2. 修改密码页面

**路径：** `/pages/user/change-password`

**功能：**
- 验证旧密码
- 设置新密码
- 确认新密码
- 密码格式验证

**测试步骤：**
1. 登录应用
2. 进入设置页面
3. 点击"修改密码"
4. 输入旧密码
5. 输入新密码（符合格式要求）
6. 确认新密码
7. 点击"确认修改"
8. 验证是否跳转到登录页面

## 密码格式要求

- 长度：6-20个字符
- 必须包含：字母和数字
- 可选包含：特殊字符 @$!%*?&

## 错误处理

### 常见错误情况

1. **邮箱已被使用**
   - 错误码：400
   - 错误信息："邮箱已被其他用户使用"

2. **手机号已被使用**
   - 错误码：400
   - 错误信息："手机号已被其他用户使用"

3. **旧密码错误**
   - 错误码：400
   - 错误信息："旧密码错误"

4. **新密码格式不正确**
   - 错误码：400
   - 错误信息："新密码必须包含字母和数字，长度6-20位"

5. **两次密码不一致**
   - 错误码：400
   - 错误信息："两次密码输入不一致"

## 数据验证

### 后端验证

- 邮箱格式验证
- 手机号格式验证（中国大陆）
- 密码强度验证
- 用户存在性验证
- 权限验证

### 前端验证

- 实时表单验证
- 密码格式提示
- 用户友好的错误提示
- 防重复提交

## 安全考虑

1. **密码处理**
   - 后端使用BCrypt加密存储
   - 前端不缓存密码信息
   - 修改密码后强制重新登录

2. **权限控制**
   - 只能修改自己的资料
   - 需要验证旧密码才能修改新密码
   - 登录状态验证

3. **数据验证**
   - 前后端双重验证
   - SQL注入防护
   - XSS防护

## 测试用例

### 个人资料更新测试

1. **正常更新**
   - 修改昵称 ✓
   - 修改邮箱 ✓
   - 修改手机号 ✓
   - 修改头像 ✓

2. **异常情况**
   - 邮箱格式错误 ✓
   - 手机号格式错误 ✓
   - 邮箱已被使用 ✓
   - 手机号已被使用 ✓

### 密码修改测试

1. **正常修改**
   - 旧密码正确，新密码符合要求 ✓

2. **异常情况**
   - 旧密码错误 ✓
   - 新密码格式不正确 ✓
   - 两次密码不一致 ✓
   - 新密码与旧密码相同 ✓

## 注意事项

1. 确保后端服务正在运行
2. 确保数据库连接正常
3. 测试前请先注册并登录用户
4. 修改密码后需要重新登录
5. 头像上传功能需要配置文件存储服务
