-- ========================================
-- 情感回复助手 - 完整部署脚本
-- 创建时间: 2024-01-15
-- 作者: YUMU
-- 说明: 只包含核心必要的表结构和初始数据
-- ========================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS emotional_reply_db
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE emotional_reply_db;

-- ========================================
-- 删除多余的表（如果存在）
-- ========================================
DROP TABLE IF EXISTS api_call_log;
DROP TABLE IF EXISTS emotion_statistics;
DROP TABLE IF EXISTS user_stats;

-- ========================================
-- 1. 核心表结构
-- ========================================

-- 用户表
DROP TABLE IF EXISTS users;
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(100) DEFAULT NULL COMMENT '昵称',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    avatar VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    status TINYINT DEFAULT 0 COMMENT '用户状态：0-正常，1-禁用',
    is_vip TINYINT DEFAULT 0 COMMENT '是否VIP：0-普通用户，1-VIP用户',
    is_admin TINYINT DEFAULT 0 COMMENT '是否管理员：0-普通用户，1-管理员',
    vip_expire_time DATETIME DEFAULT NULL COMMENT 'VIP过期时间',
    daily_quota INT DEFAULT 5 COMMENT '每日配额',
    today_used INT DEFAULT 0 COMMENT '今日已使用次数',
    total_used INT DEFAULT 0 COMMENT '总使用次数',
    last_login_time DATETIME DEFAULT NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志：0-未删除，1-已删除',

    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 回复历史表
DROP TABLE IF EXISTS reply_history;
CREATE TABLE reply_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    original_message TEXT NOT NULL COMMENT '原始消息',
    emotion_result VARCHAR(50) DEFAULT NULL COMMENT '情感分析结果',
    emotion_confidence DECIMAL(5,2) DEFAULT NULL COMMENT '情感置信度',
    reply_list JSON DEFAULT NULL COMMENT '生成的回复列表（JSON格式）',
    selected_reply TEXT DEFAULT NULL COMMENT '用户选择的回复',
    selected_style VARCHAR(50) DEFAULT NULL COMMENT '选择的回复风格',
    is_favorite TINYINT DEFAULT 0 COMMENT '是否收藏：0-未收藏，1-已收藏',
    user_rating TINYINT DEFAULT NULL COMMENT '用户评分：1-5分',
    user_feedback TEXT DEFAULT NULL COMMENT '用户反馈',
    process_time BIGINT DEFAULT NULL COMMENT '处理耗时（毫秒）',
    client_ip VARCHAR(50) DEFAULT NULL COMMENT '客户端IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志：0-未删除，1-已删除',
    
    INDEX idx_user_id (user_id),
    INDEX idx_emotion_result (emotion_result),
    INDEX idx_is_favorite (is_favorite),
    INDEX idx_create_time (create_time),
    INDEX idx_user_create (user_id, create_time),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回复历史表';

-- 用户设置表
DROP TABLE IF EXISTS user_settings;
CREATE TABLE user_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设置ID',
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    theme VARCHAR(20) DEFAULT 'light' COMMENT '主题：light-浅色，dark-深色',
    font_size VARCHAR(20) DEFAULT 'medium' COMMENT '字体大小：small-小，medium-中，large-大',
    auto_save TINYINT DEFAULT 1 COMMENT '自动保存：0-关闭，1-开启',
    show_floating_bubble TINYINT DEFAULT 0 COMMENT '显示悬浮气泡：0-关闭，1-开启',
    reply_styles JSON DEFAULT NULL COMMENT '偏好的回复风格列表',
    preferred_reply_count TINYINT DEFAULT 2 COMMENT '偏好生成回复数量：1-3个',
    reply_generation_mode VARCHAR(20) DEFAULT 'smart' COMMENT '回复生成模式：smart-智能选择，custom-自定义风格，single-单一风格',
    primary_style VARCHAR(50) DEFAULT 'warm_caring' COMMENT '主要回复风格',
    notification_enabled TINYINT DEFAULT 1 COMMENT '通知开启：0-关闭，1-开启',
    notification_sound TINYINT DEFAULT 1 COMMENT '通知声音：0-关闭，1-开启',
    notification_vibrate TINYINT DEFAULT 1 COMMENT '通知震动：0-关闭，1-开启',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设置表';

-- 系统配置表
DROP TABLE IF EXISTS system_config;
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT DEFAULT NULL COMMENT '配置值',
    config_desc VARCHAR(255) DEFAULT NULL COMMENT '配置描述',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型：string-字符串，number-数字，boolean-布尔，json-JSON',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：0-私有，1-公开',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 激活码表
DROP TABLE IF EXISTS activation_codes;
CREATE TABLE activation_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '激活码ID',
    code VARCHAR(32) UNIQUE NOT NULL COMMENT '激活码',
    code_type VARCHAR(20) NOT NULL COMMENT '激活码类型：vip_1m-1个月VIP，vip_3m-3个月VIP，vip_6m-6个月VIP，vip_1y-1年VIP',
    duration_days INT NOT NULL COMMENT '有效天数',
    batch_id VARCHAR(64) COMMENT '批次ID',
    created_by BIGINT NOT NULL COMMENT '创建者(管理员ID)',
    used_by BIGINT COMMENT '使用者用户ID',
    used_time DATETIME COMMENT '使用时间',
    expire_time DATETIME NOT NULL COMMENT '激活码过期时间',
    status TINYINT DEFAULT 0 COMMENT '状态：0-未使用，1-已使用，2-已过期',
    remark VARCHAR(255) COMMENT '备注信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_code (code),
    INDEX idx_batch_id (batch_id),
    INDEX idx_created_by (created_by),
    INDEX idx_used_by (used_by),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time),
    INDEX idx_code_type (code_type),

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='激活码表';

-- ========================================
-- 2. 初始数据
-- ========================================

-- 插入默认用户（用于测试）
INSERT INTO users (username, nickname, email, password, daily_quota, status, is_admin, is_vip, vip_expire_time) VALUES
('admin', '管理员', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', -1, 0, 1, 0, NULL),
('testuser', '测试用户', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', 5, 0, 0, 0, NULL),
('vipuser', 'VIP用户', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', 50, 0, 0, 1, DATE_ADD(NOW(), INTERVAL 1 YEAR));

-- 插入用户设置
INSERT INTO user_settings (user_id, theme, font_size, auto_save, reply_styles, preferred_reply_count, reply_generation_mode, primary_style) VALUES
(1, 'light', 'medium', 1, '["warm_caring", "humorous", "high_eq"]', 2, 'custom', 'warm_caring'),
(2, 'dark', 'large', 1, '["mature", "direct", "high_eq"]', 1, 'single', 'mature'),
(3, 'light', 'medium', 1, '["romantic", "warm_caring", "humorous"]', 3, 'custom', 'romantic');

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, config_desc, config_type, is_public) VALUES
('default_daily_quota', '5', '普通用户每日配额', 'number', 1),
('vip_daily_quota', '50', 'VIP用户每日配额', 'number', 1),
('admin_daily_quota', '-1', '管理员每日配额（-1表示无限制）', 'number', 1),
('max_message_length', '1000', '最大消息长度', 'number', 1),
('supported_emotions', '["开心", "难过", "愤怒", "担心", "兴奋", "平静", "关心", "感谢"]', '支持的情感类型', 'json', 1),
('supported_reply_styles', '{"warm_caring": "暖男", "humorous": "逗比", "romantic": "撩女生", "high_eq": "高情商", "direct": "直接", "mature": "成熟稳重", "gentle": "温柔大叔", "dominant": "霸道总裁", "literary": "发表文学", "detailed": "话痨延申"}', '支持的回复风格', 'json', 1),
('enable_user_registration', 'true', '是否开启用户注册', 'boolean', 1),
('enable_guest_access', 'true', '是否允许游客访问', 'boolean', 1),
('api_rate_limit', '60', 'API调用频率限制（每分钟）', 'number', 0),
('maintenance_mode', 'false', '维护模式', 'boolean', 0),
('app_version', '1.0.0', '应用版本', 'string', 1),
('activation_code_types', '{"vip_1m": "1个月VIP", "vip_3m": "3个月VIP", "vip_6m": "6个月VIP", "vip_1y": "1年VIP"}', '激活码类型配置', 'json', 1);

-- ========================================
-- 2. 初始数据
-- ========================================

-- 插入默认用户（用于测试）
INSERT INTO users (username, nickname, email, password, daily_quota, status, is_admin, is_vip, vip_expire_time) VALUES
('admin', '管理员', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', -1, 0, 1, 0, NULL),
('testuser', '测试用户', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', 5, 0, 0, 0, NULL),
('vipuser', 'VIP用户', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', 50, 0, 0, 1, DATE_ADD(NOW(), INTERVAL 1 YEAR));

-- 插入用户设置
INSERT INTO user_settings (user_id, theme, font_size, auto_save, reply_styles, preferred_reply_count, reply_generation_mode, primary_style) VALUES
(1, 'dark', 'large', 1, '["mature", "direct", "high_eq"]', 1, 'single', 'mature'),
(2, 'light', 'medium', 1, '["warm_caring", "humorous", "high_eq"]', 2, 'custom', 'warm_caring'),
(3, 'light', 'medium', 1, '["romantic", "warm_caring", "humorous"]', 3, 'custom', 'romantic');

-- 插入示例回复历史（用于测试）
INSERT INTO reply_history (user_id, original_message, emotion_result, emotion_confidence, reply_list, selected_reply, selected_style, is_favorite, process_time, client_ip) VALUES
(2, '今天心情不太好', '难过', 85.5,
'[{"content":"听起来你遇到了一些困难，我很关心你。如果需要帮助或者想聊聊，我随时都在。","style":"warm_caring","styleName":"暖男"},{"content":"虽然现在有点难过，但记住，彩虹总在风雨后！🌈","style":"humorous","styleName":"逗比"}]',
'听起来你遇到了一些困难，我很关心你。如果需要帮助或者想聊聊，我随时都在。', 'warm_caring', 1, 1500, '127.0.0.1'),

(3, '晚上一起吃饭吗？', '关心', 75.3,
'[{"content":"你的关心是我最珍贵的礼物，我也同样关心着你。💕","style":"romantic","styleName":"撩女生"},{"content":"感受到了你的关心，真的很温暖。谢谢你这么体贴。","style":"warm_caring","styleName":"暖男"}]',
'你的关心是我最珍贵的礼物，我也同样关心着你。💕', 'romantic', 1, 950, '********');

-- 插入示例激活码（用于测试）
INSERT INTO activation_codes (code, code_type, duration_days, batch_id, created_by, expire_time, status, remark) VALUES
('VIP1-2024-ABCD-EFGH', 'vip_1m', 30, 'BATCH-2024-001', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '1个月VIP测试激活码'),
('VIP3-2024-IJKL-MNOP', 'vip_3m', 90, 'BATCH-2024-001', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '3个月VIP测试激活码'),
('VIP6-2024-QRST-UVWX', 'vip_6m', 180, 'BATCH-2024-002', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '6个月VIP测试激活码'),
('VIP1Y-2024-YZAB-CDEF', 'vip_1y', 365, 'BATCH-2024-002', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 0, '1年VIP测试激活码'),
('USED-2024-TEST-CODE', 'vip_1m', 30, 'BATCH-2024-TEST', 1, DATE_ADD(NOW(), INTERVAL 6 MONTH), 1, '已使用的测试激活码');

-- ========================================
-- 3. 部署完成提示
-- ========================================

-- 查看创建的表
SHOW TABLES;

-- 查看用户配额设置
SELECT
    id,
    username,
    nickname,
    daily_quota,
    is_vip,
    is_admin,
    CASE
        WHEN is_admin = 1 THEN '管理员（无限制）'
        WHEN is_vip = 1 THEN 'VIP用户（50次/天）'
        ELSE '普通用户（5次/天）'
    END as user_type
FROM users
ORDER BY is_admin DESC, is_vip DESC, id;

-- 查看系统配置
SELECT config_key, config_value, config_desc
FROM system_config
WHERE config_key LIKE '%quota%' OR config_key LIKE '%activation%'
ORDER BY config_key;

-- 查看激活码
SELECT
    code,
    code_type,
    duration_days,
    status,
    CASE
        WHEN status = 0 THEN '未使用'
        WHEN status = 1 THEN '已使用'
        WHEN status = 2 THEN '已过期'
        ELSE '未知状态'
    END as status_text,
    remark
FROM activation_codes
ORDER BY status, code_type;

-- ========================================
-- 部署脚本执行完成！
--
-- 默认用户账号：
-- 1. 管理员：admin / 123456 （无限制配额）
-- 2. VIP用户：vipuser / 123456 （50次/天）
-- 3. 普通用户：testuser / 123456 （5次/天）
--
-- 测试激活码：
-- 1. VIP1-2024-ABCD-EFGH （1个月VIP）
-- 2. VIP3-2024-IJKL-MNOP （3个月VIP）
-- 3. VIP6-2024-QRST-UVWX （6个月VIP）
-- 4. VIP1Y-2024-YZAB-CDEF （1年VIP）
--
-- 注意：密码已加密，实际密码为 123456
-- ========================================
