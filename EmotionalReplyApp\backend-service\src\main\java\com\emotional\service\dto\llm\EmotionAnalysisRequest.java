package com.emotional.service.dto.llm;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 情感分析请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmotionAnalysisRequest {
    
    /**
     * 要分析的消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 2000, message = "消息内容不能超过2000个字符")
    private String message;
    
    /**
     * 用户ID（可选，用于个性化分析）
     */
    private Long userId;
    
    /**
     * 分析语言（默认中文）
     */
    private String language = "zh-CN";
    
    /**
     * 分析维度
     */
    private List<String> dimensions;
    
    /**
     * 上下文信息（可选）
     */
    private String context;
    
    /**
     * 是否需要详细分析
     */
    private Boolean detailedAnalysis = false;
    
    /**
     * 温度参数（控制创造性，0.0-1.0）
     */
    private Double temperature = 0.3;
}
