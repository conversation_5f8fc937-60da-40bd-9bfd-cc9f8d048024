-- 更新用户配额设置脚本
-- 执行时间: 2024-01-15
-- 作用: 将现有用户的配额更新为新的标准

USE emotional_reply_db;

-- 更新普通用户的配额为5次
UPDATE users 
SET daily_quota = 5 
WHERE is_admin = 0 AND is_vip = 0;

-- 更新VIP用户的配额为50次
UPDATE users 
SET daily_quota = 50 
WHERE is_vip = 1 AND is_admin = 0;

-- 更新管理员的配额为无限制（-1）
UPDATE users 
SET daily_quota = -1 
WHERE is_admin = 1;

-- 更新系统配置
UPDATE system_config 
SET config_value = '5' 
WHERE config_key = 'default_daily_quota';

UPDATE system_config 
SET config_desc = '普通用户每日配额' 
WHERE config_key = 'default_daily_quota';

-- 如果不存在管理员配额配置，则插入
INSERT IGNORE INTO system_config (config_key, config_value, config_desc, config_type, is_public) 
VALUES ('admin_daily_quota', '-1', '管理员每日配额（-1表示无限制）', 'number', 1);

-- 查看更新结果
SELECT 
    id,
    username,
    nickname,
    daily_quota,
    is_vip,
    is_admin,
    CASE 
        WHEN is_admin = 1 THEN '管理员（无限制）'
        WHEN is_vip = 1 THEN 'VIP用户（50次/天）'
        ELSE '普通用户（5次/天）'
    END as user_type
FROM users 
ORDER BY is_admin DESC, is_vip DESC, id;

-- 查看系统配置
SELECT config_key, config_value, config_desc 
FROM system_config 
WHERE config_key LIKE '%quota%' 
ORDER BY config_key;
