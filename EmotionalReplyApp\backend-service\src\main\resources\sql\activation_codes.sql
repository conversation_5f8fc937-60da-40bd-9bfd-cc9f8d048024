-- 激活码表
CREATE TABLE IF NOT EXISTS activation_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '激活码ID',
    code VARCHAR(32) UNIQUE NOT NULL COMMENT '激活码',
    code_type VARCHAR(20) NOT NULL COMMENT '激活码类型',
    duration_days INT NOT NULL COMMENT '有效天数',
    batch_id VARCHAR(64) COMMENT '批次ID',
    created_by BIGINT NOT NULL COMMENT '创建者(管理员ID)',
    used_by BIGINT COMMENT '使用者用户ID',
    used_time DATETIME COMMENT '使用时间',
    expire_time DATETIME NOT NULL COMMENT '激活码过期时间',
    status TINYINT DEFAULT 0 COMMENT '状态 0:未使用 1:已使用 2:已过期',
    remark VARCHAR(255) COMMENT '备注信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_code (code),
    INDEX idx_batch_id (batch_id),
    INDEX idx_created_by (created_by),
    INDEX idx_used_by (used_by),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='激活码表';

