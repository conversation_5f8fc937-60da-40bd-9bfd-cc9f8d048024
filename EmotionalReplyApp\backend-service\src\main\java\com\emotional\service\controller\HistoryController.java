package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.entity.ReplyHistory;
import com.emotional.service.entity.User;
import com.emotional.service.service.ReplyHistoryService;
import com.emotional.service.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 历史记录控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/history")
@RequiredArgsConstructor
@Validated
@CrossOrigin(originPatterns = "*", maxAge = 3600)
public class HistoryController {

    private final ReplyHistoryService replyHistoryService;
    private final UserService userService;
    
    /**
     * 获取用户历史记录
     */
    @GetMapping("/list")
    public Result<List<ReplyHistory>> getHistory(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            List<ReplyHistory> historyList = replyHistoryService.getHistoryByUserId(userId, page, size);
            return Result.success("获取成功", historyList);
        } catch (Exception e) {
            log.error("获取历史记录失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户收藏的历史记录
     */
    @GetMapping("/favorites")
    public Result<List<ReplyHistory>> getFavorites(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            List<ReplyHistory> favoriteList = replyHistoryService.getFavoriteHistoryByUserId(userId, page, size);
            return Result.success("获取成功", favoriteList);
        } catch (Exception e) {
            log.error("获取收藏记录失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 切换收藏状态
     */
    @PostMapping("/{historyId}/favorite")
    public Result<Boolean> toggleFavorite(
            @PathVariable @NotNull Long historyId,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            boolean success = replyHistoryService.toggleFavorite(historyId, userId);
            if (success) {
                return Result.success("操作成功", true);
            } else {
                return Result.error("操作失败，记录不存在或无权限");
            }
        } catch (Exception e) {
            log.error("切换收藏状态失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除历史记录
     */
    @DeleteMapping("/{historyId}")
    public Result<Boolean> deleteHistory(
            @PathVariable @NotNull Long historyId,
            @RequestParam(defaultValue = "1") Long userId) {
        
        try {
            boolean success = replyHistoryService.deleteHistory(historyId, userId);
            if (success) {
                return Result.success("删除成功", true);
            } else {
                return Result.error("删除失败，记录不存在或无权限");
            }
        } catch (Exception e) {
            log.error("删除历史记录失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交用户反馈
     */
    @PostMapping("/{historyId}/feedback")
    public Result<Boolean> submitFeedback(
            @PathVariable @NotNull Long historyId,
            @RequestParam(defaultValue = "1") Long userId,
            @RequestParam @Min(1) @Max(5) Integer rating,
            @RequestParam(required = false) String feedback) {
        
        try {
            boolean success = replyHistoryService.submitFeedback(historyId, userId, rating, feedback);
            if (success) {
                return Result.success("反馈提交成功", true);
            } else {
                return Result.error("提交失败，记录不存在或无权限");
            }
        } catch (Exception e) {
            log.error("提交反馈失败", e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户使用统计
     */
    @GetMapping("/stats")
    public Result<UserStatsResponse> getUserStats(@RequestParam(defaultValue = "1") Long userId) {
        try {
            int todayUsageCount = replyHistoryService.getTodayUsageCount(userId);
            int totalUsageCount = replyHistoryService.getTotalUsageCount(userId);

            // 根据用户角色获取配额
            int dailyQuota = getUserDailyQuota(userId);

            UserStatsResponse stats = new UserStatsResponse();
            stats.setTodayUsage(todayUsageCount);
            stats.setTotalUsage(totalUsageCount);
            stats.setDailyQuota(dailyQuota);
            stats.setRemainingQuota(Math.max(0, dailyQuota - todayUsageCount));

            return Result.success("获取成功", stats);
        } catch (Exception e) {
            log.error("获取用户统计失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取每日配额
     */
    private int getUserDailyQuota(Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return 10; // 默认配额
            }

            // 管理员无限制，返回一个大数值用于显示
            if (user.getIsAdmin() != null && user.getIsAdmin() == 1) {
                return 9999; // 显示为9999，表示无限制
            }

            // VIP用户配额
            if (user.getIsVip() != null && user.getIsVip() == 1) {
                return 150; // VIP配额
            }

            // 普通用户配额
            return 10;

        } catch (Exception e) {
            log.error("获取用户配额失败: userId={}", userId, e);
            return 10; // 出错时返回默认配额
        }
    }

    /**
     * 用户统计响应类
     */
    public static class UserStatsResponse {
        private int todayUsage;
        private int totalUsage;
        private int dailyQuota;
        private int remainingQuota;

        // Getters and Setters
        public int getTodayUsage() {
            return todayUsage;
        }

        public void setTodayUsage(int todayUsage) {
            this.todayUsage = todayUsage;
        }

        public int getTotalUsage() {
            return totalUsage;
        }

        public void setTotalUsage(int totalUsage) {
            this.totalUsage = totalUsage;
        }

        public int getDailyQuota() {
            return dailyQuota;
        }

        public void setDailyQuota(int dailyQuota) {
            this.dailyQuota = dailyQuota;
        }

        public int getRemainingQuota() {
            return remainingQuota;
        }

        public void setRemainingQuota(int remainingQuota) {
            this.remainingQuota = remainingQuota;
        }
    }
}
