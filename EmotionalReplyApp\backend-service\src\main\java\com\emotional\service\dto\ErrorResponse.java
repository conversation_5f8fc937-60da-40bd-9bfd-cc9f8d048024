package com.emotional.service.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 错误响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponse {
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 用户友好的错误信息
     */
    private String userMessage;
    
    /**
     * 错误类型
     */
    private ErrorType errorType;
    
    /**
     * 是否需要联系管理员
     */
    private Boolean needContactAdmin;
    
    /**
     * 建议的解决方案
     */
    private String suggestion;
    
    /**
     * 发生时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        API_KEY_INVALID("API_KEY_INVALID", "API密钥无效"),
        INSUFFICIENT_BALANCE("INSUFFICIENT_BALANCE", "余额不足"),
        RATE_LIMIT("RATE_LIMIT", "请求频率限制"),
        SERVICE_UNAVAILABLE("SERVICE_UNAVAILABLE", "服务不可用"),
        NETWORK_ERROR("NETWORK_ERROR", "网络错误"),
        UNKNOWN_ERROR("UNKNOWN_ERROR", "未知错误");
        
        private final String code;
        private final String description;
        
        ErrorType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建API密钥错误响应
     */
    public static ErrorResponse apiKeyError() {
        return ErrorResponse.builder()
                .errorCode("API_KEY_INVALID")
                .errorType(ErrorType.API_KEY_INVALID)
                .errorMessage("DeepSeek API密钥无效或已过期")
                .userMessage("AI服务暂时不可用，我们已通知管理员处理")
                .needContactAdmin(true)
                .suggestion("请联系管理员更新API密钥配置")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建余额不足错误响应
     */
    public static ErrorResponse insufficientBalanceError() {
        return ErrorResponse.builder()
                .errorCode("INSUFFICIENT_BALANCE")
                .errorType(ErrorType.INSUFFICIENT_BALANCE)
                .errorMessage("DeepSeek API余额不足")
                .userMessage("AI服务暂时不可用，我们已通知管理员处理")
                .needContactAdmin(true)
                .suggestion("请联系管理员为账户充值")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建服务不可用错误响应
     */
    public static ErrorResponse serviceUnavailableError(String details) {
        return ErrorResponse.builder()
                .errorCode("SERVICE_UNAVAILABLE")
                .errorType(ErrorType.SERVICE_UNAVAILABLE)
                .errorMessage("DeepSeek服务暂时不可用: " + details)
                .userMessage("AI服务暂时不可用，请稍后重试")
                .needContactAdmin(false)
                .suggestion("请稍后重试，如问题持续请联系管理员")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建通用错误响应
     */
    public static ErrorResponse genericError(String message) {
        return ErrorResponse.builder()
                .errorCode("UNKNOWN_ERROR")
                .errorType(ErrorType.UNKNOWN_ERROR)
                .errorMessage(message)
                .userMessage("AI服务出现异常，我们已通知管理员处理")
                .needContactAdmin(true)
                .suggestion("请联系管理员或稍后重试")
                .timestamp(LocalDateTime.now())
                .build();
    }
}
