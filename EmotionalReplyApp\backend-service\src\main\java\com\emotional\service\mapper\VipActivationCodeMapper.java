package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.VipActivationCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * VIP激活码Mapper接口
 */
@Mapper
public interface VipActivationCodeMapper extends BaseMapper<VipActivationCode> {

    /**
     * 根据激活码查询
     */
    @Select("SELECT * FROM activation_codes WHERE code = #{code}")
    VipActivationCode getByCode(@Param("code") String code);

    /**
     * 使用激活码
     */
    @Update("UPDATE activation_codes SET status = 1, used_by = #{userId}, " +
            "used_time = NOW(), updated_time = NOW() WHERE code = #{code} AND status = 0")
    int useCode(@Param("code") String code, @Param("userId") Long userId);

    /**
     * 检查激活码是否可用
     */
    @Select("SELECT COUNT(*) FROM activation_codes WHERE code = #{code} " +
            "AND status = 0 AND expire_time > NOW()")
    int checkCodeAvailable(@Param("code") String code);
}
