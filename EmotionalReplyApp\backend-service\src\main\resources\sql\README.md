# 数据库部署说明

## 📁 文件说明

- `deploy.sql` - **完整部署脚本**（包含核心表结构和初始数据）

## 🚀 部署步骤

### 1. 执行部署脚本
```bash
# 命令行方式
mysql -u root -p < deploy.sql

# 或者在数据库管理工具中执行 deploy.sql 文件
```

### 2. 验证部署
执行脚本后会自动显示：
- 创建的表列表
- 用户配额设置
- 系统配置信息

## 👥 默认用户账号

| 用户类型 | 用户名 | 密码 | 每日配额 | 说明 |
|---------|--------|------|----------|------|
| 管理员 | admin | 123456 | 无限制 | 系统管理员 |
| VIP用户 | vipuser | 123456 | 50次/天 | VIP测试账号 |
| 普通用户 | testuser | 123456 | 5次/天 | 普通测试账号 |

## 🎫 测试激活码

| 激活码 | 类型 | 有效期 | 状态 | 说明 |
|--------|------|--------|------|------|
| VIP1-2024-ABCD-EFGH | vip_1m | 30天 | 未使用 | 1个月VIP测试码 |
| VIP3-2024-IJKL-MNOP | vip_3m | 90天 | 未使用 | 3个月VIP测试码 |
| VIP6-2024-QRST-UVWX | vip_6m | 180天 | 未使用 | 6个月VIP测试码 |
| VIP1Y-2024-YZAB-CDEF | vip_1y | 365天 | 未使用 | 1年VIP测试码 |
| USED-2024-TEST-CODE | vip_1m | 30天 | 已使用 | 已使用的测试码 |

## 📊 配额设置

- **普通用户**：5次/天
- **VIP用户**：50次/天
- **管理员**：无限制（-1表示无限制）

## 🗄️ 核心表结构

1. **users** - 用户信息和配额管理
2. **reply_history** - 使用记录和历史数据
3. **user_settings** - 用户个性化设置
4. **system_config** - 系统配置参数
5. **activation_codes** - VIP激活码管理

## 🗑️ 已删除的多余表

为了简化部署和维护，已删除以下功能重复的表：
- `api_call_log` - API调用日志（功能重复）
- `emotion_statistics` - 情感统计（可从reply_history统计）
- `user_stats` - 用户统计（功能重复）

## ⚠️ 注意事项

1. **备份数据**：部署前请备份现有数据库
2. **权限检查**：确保MySQL用户有创建数据库和表的权限
3. **字符集**：使用utf8mb4字符集支持emoji表情
4. **密码安全**：生产环境请修改默认密码

## 🔧 故障排除

### 常见问题
1. **权限不足**：确保MySQL用户有足够权限
2. **字符集问题**：确保数据库支持utf8mb4
3. **外键约束**：确保按顺序执行SQL语句

### 重新部署
如需重新部署，可以先删除数据库：
```sql
DROP DATABASE IF EXISTS emotional_reply_db;
```
然后重新执行 `deploy.sql`。
