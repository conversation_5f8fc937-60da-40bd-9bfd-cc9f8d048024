# 情感回复助手 App

## 📱 项目简介

这是一个基于AI的智能回复生成系统，帮助用户根据不同情感和场景生成个性化的回复内容。

### 🎯 核心功能
- 🎭 **智能回复生成**：10种不同风格的个性化回复
- 👤 **用户管理**：普通用户、VIP用户、管理员三级权限
- 🎫 **激活码系统**：6种时长的VIP激活码（1天-1年）
- 📊 **配额管理**：普通用户5次/天，VIP用户50次/天，管理员无限制
- ⚙️ **个性化设置**：用户可自定义回复风格和生成模式

### 🚀 快速开始
1. 复制收到的消息
2. 在app中粘贴
3. 系统分析并生成回复建议
4. 选择合适的回复
5. 一键复制使用

## 📚 完整文档

**请查看 [项目完整文档](docs/项目完整文档.md) 获取详细信息，包括：**
- 数据库设计
- 技术架构
- 部署指南
- API接口
- 使用说明

## 🏗️ 技术架构

### **前端**
- **框架**: uni-app (Vue 3)
- **支持平台**: Android、iOS、H5、微信小程序
- **状态管理**: Vuex
- **UI组件**: uni-ui + 自定义组件

### **后端**
- **框架**: Spring Boot 2.7+
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis-Plus
- **AI服务**: DeepSeek API

## ⚡ 快速部署

### **数据库初始化**
```bash
mysql -u root -p emotional_reply < backend-service/src/main/resources/sql/deploy.sql
```

### **后端启动**
```bash
cd backend-service
mvn spring-boot:run
```

### **前端启动**
```bash
cd uniapp-frontend
npm install
npm run dev:h5
```

## 🎯 测试账号

| 用户类型 | 用户名 | 密码 | 配额 |
|---------|--------|------|------|
| 管理员 | admin | 123456 | 无限制 |
| VIP用户 | vipuser | 123456 | 50次/天 |
| 普通用户 | testuser | 123456 | 5次/天 |

## 📄 许可证

本项目采用 MIT 许可证

## 联系方式

如有问题或建议，请提交Issue。
