/**
 * 网络请求封装
 */

// 简单的参数序列化函数 - 替代URLSearchParams
function serializeParams(params) {
  if (!params || typeof params !== 'object') {
    return ''
  }

  const pairs = []
  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key]
      if (value !== null && value !== undefined && value !== '') {
        pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
      }
    }
  }

  return pairs.join('&')
}

// 基础配置
const BASE_URL = 'http://localhost:8080/api'
const TIMEOUT = 10000

// 调试信息已移除

// 请求拦截器
const requestInterceptor = (config) => {
  // 添加 token
  const token = uni.getStorageSync('token')
  if (token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${token}`
    }
  }
  
  // 添加通用请求头
  config.header = {
    'Content-Type': 'application/json',
    ...config.header
  }

  return config
}

// 响应拦截器
const responseInterceptor = (response) => {
  const { data, statusCode } = response
  
  // HTTP 状态码检查
  if (statusCode !== 200) {
    handleHttpError(statusCode, data)
    // 创建一个包含错误信息的Error对象
    const error = new Error(data?.message || `HTTP错误: ${statusCode}`)
    error.statusCode = statusCode
    error.data = data
    return Promise.reject(error)
  }
  
  // 业务状态码检查
  if (data.code !== 200) {
    handleBusinessError(data.code, data.message)
    // 创建一个包含错误信息的Error对象
    const error = new Error(data.message || '操作失败')
    error.code = data.code
    error.data = data
    return Promise.reject(error)
  }
  
  return data.data
}

// HTTP 错误处理
const handleHttpError = (statusCode, data) => {
  let message = '网络请求失败'
  
  switch (statusCode) {
    case 401:
      message = '登录已过期，请重新登录'
      // 清除本地存储的用户信息
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      // 跳转到登录页
      uni.navigateTo({
        url: '/pages/login/login'
      })
      break
    case 403:
      message = '没有权限访问'
      break
    case 404:
      message = '请求的资源不存在'
      break
    case 500:
      message = '服务器内部错误'
      break
    default:
      message = data?.message || `请求失败 (${statusCode})`
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
}

// 业务错误处理
const handleBusinessError = (code, message) => {
  // 对于某些特殊错误，不显示toast，让页面自己处理
  const silentErrors = [500] // 服务器错误让页面自己处理

  if (!silentErrors.includes(code)) {
    uni.showToast({
      title: message || '操作失败',
      icon: 'none',
      duration: 3000
    })
  }
}

// 封装请求方法
export const request = (options) => {
  return new Promise((resolve, reject) => {
    // 应用请求拦截器
    const config = requestInterceptor({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: options.header || {},
      timeout: options.timeout || TIMEOUT
    })

    // 发送请求
    
    uni.request({
      ...config,
      success: (response) => {
        try {
          const result = responseInterceptor(response)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        console.error('网络请求失败:', error)
        console.error('请求URL:', config.url)
        console.error('请求方法:', config.method)
        console.error('请求数据:', config.data)
        console.error('请求头:', config.header)
        console.error('完整错误信息:', JSON.stringify(error, null, 2))

        let errorMessage = '网络连接失败'

        // 根据错误类型提供更具体的提示
        if (error.errMsg) {
          console.error('错误消息:', error.errMsg)
          if (error.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接'
          } else if (error.errMsg.includes('fail')) {
            errorMessage = '无法连接到服务器，请检查网络设置'
          } else if (error.errMsg.includes('abort')) {
            errorMessage = '请求被中断'
          } else if (error.errMsg.includes('cors')) {
            errorMessage = 'CORS跨域错误'
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
        reject(error)
      }
    })
  })
}

// GET 请求
export const get = (url, params = {}) => {
  return request({
    url: url + (Object.keys(params).length ? '?' + serializeParams(params) : ''),
    method: 'GET'
  })
}

// POST 请求
export const post = (url, data = {}) => {
  return request({
    url,
    method: 'POST',
    data
  })
}

// PUT 请求
export const put = (url, data = {}) => {
  return request({
    url,
    method: 'PUT',
    data
  })
}

// DELETE 请求
export const del = (url, params = {}) => {
  return request({
    url: url + (Object.keys(params).length ? '?' + serializeParams(params) : ''),
    method: 'DELETE'
  })
}

// 文件上传
export const upload = (url, filePath, formData = {}) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token')
    
    uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name: 'file',
      formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (response) => {
        try {
          const data = JSON.parse(response.data)
          if (data.code === 200) {
            resolve(data.data)
          } else {
            handleBusinessError(data.code, data.message)
            reject(data)
          }
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

// 默认导出
export default {
  request,
  get,
  post,
  put,
  del,
  upload
}
